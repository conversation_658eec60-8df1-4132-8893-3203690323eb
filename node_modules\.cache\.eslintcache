[{"E:\\Kimi\\src\\index.tsx": "1", "E:\\Kimi\\src\\App.tsx": "2", "E:\\Kimi\\src\\store\\store.ts": "3", "E:\\Kimi\\src\\i18n\\i18n.ts": "4", "E:\\Kimi\\src\\contexts\\I18nContext.tsx": "5", "E:\\Kimi\\src\\store\\slices\\companySlice.ts": "6", "E:\\Kimi\\src\\store\\slices\\authSlice.ts": "7", "E:\\Kimi\\src\\store\\slices\\accountsSlice.ts": "8", "E:\\Kimi\\src\\store\\slices\\customersSlice.ts": "9", "E:\\Kimi\\src\\store\\slices\\suppliersSlice.ts": "10", "E:\\Kimi\\src\\store\\slices\\productsSlice.ts": "11", "E:\\Kimi\\src\\store\\slices\\invoicesSlice.ts": "12", "E:\\Kimi\\src\\components\\Layout\\Layout.tsx": "13", "E:\\Kimi\\src\\pages\\Auth\\LoginPage.tsx": "14", "E:\\Kimi\\src\\pages\\Accounting\\ChartOfAccounts.tsx": "15", "E:\\Kimi\\src\\pages\\Dashboard\\Dashboard.tsx": "16", "E:\\Kimi\\src\\pages\\CRM\\Suppliers.tsx": "17", "E:\\Kimi\\src\\pages\\Customers\\CustomersPage.tsx": "18", "E:\\Kimi\\src\\pages\\Inventory\\Products.tsx": "19", "E:\\Kimi\\src\\pages\\Sales\\Invoices.tsx": "20", "E:\\Kimi\\src\\pages\\Purchases\\Purchases.tsx": "21", "E:\\Kimi\\src\\pages\\Finance\\Payments.tsx": "22", "E:\\Kimi\\src\\pages\\HR\\Employees.tsx": "23", "E:\\Kimi\\src\\pages\\HR\\Payroll.tsx": "24", "E:\\Kimi\\src\\pages\\Assets\\FixedAssets.tsx": "25", "E:\\Kimi\\src\\pages\\Reports\\Reports.tsx": "26", "E:\\Kimi\\src\\pages\\Settings\\Settings.tsx": "27", "E:\\Kimi\\src\\api\\database.ts": "28", "E:\\Kimi\\src\\components\\Layout\\Sidebar.tsx": "29"}, {"size": 2941, "mtime": *************, "results": "30", "hashOfConfig": "31"}, {"size": 4773, "mtime": *************, "results": "32", "hashOfConfig": "31"}, {"size": 959, "mtime": *************, "results": "33", "hashOfConfig": "31"}, {"size": 618, "mtime": 1752701218176, "results": "34", "hashOfConfig": "31"}, {"size": 1804, "mtime": 1752701206874, "results": "35", "hashOfConfig": "31"}, {"size": 9463, "mtime": 1752701839525, "results": "36", "hashOfConfig": "31"}, {"size": 7843, "mtime": 1752705285726, "results": "37", "hashOfConfig": "31"}, {"size": 18518, "mtime": 1752702509924, "results": "38", "hashOfConfig": "31"}, {"size": 12756, "mtime": 1752701977481, "results": "39", "hashOfConfig": "31"}, {"size": 12726, "mtime": 1752702125461, "results": "40", "hashOfConfig": "31"}, {"size": 14415, "mtime": 1752702210662, "results": "41", "hashOfConfig": "31"}, {"size": 16617, "mtime": 1752702319865, "results": "42", "hashOfConfig": "31"}, {"size": 9165, "mtime": 1752705532893, "results": "43", "hashOfConfig": "31"}, {"size": 11000, "mtime": 1752705508132, "results": "44", "hashOfConfig": "31"}, {"size": 11306, "mtime": 1752703354712, "results": "45", "hashOfConfig": "31"}, {"size": 15518, "mtime": 1752701792037, "results": "46", "hashOfConfig": "31"}, {"size": 14338, "mtime": 1752703412584, "results": "47", "hashOfConfig": "31"}, {"size": 17891, "mtime": 1752703044174, "results": "48", "hashOfConfig": "31"}, {"size": 13512, "mtime": 1752703466825, "results": "49", "hashOfConfig": "31"}, {"size": 5891, "mtime": 1752703498017, "results": "50", "hashOfConfig": "31"}, {"size": 530, "mtime": 1752703512218, "results": "51", "hashOfConfig": "31"}, {"size": 528, "mtime": 1752703525757, "results": "52", "hashOfConfig": "31"}, {"size": 8248, "mtime": 1752703587976, "results": "53", "hashOfConfig": "31"}, {"size": 12087, "mtime": 1752703636181, "results": "54", "hashOfConfig": "31"}, {"size": 12071, "mtime": 1752703686656, "results": "55", "hashOfConfig": "31"}, {"size": 9824, "mtime": 1752703730425, "results": "56", "hashOfConfig": "31"}, {"size": 17088, "mtime": 1752703789409, "results": "57", "hashOfConfig": "31"}, {"size": 16561, "mtime": 1752705234486, "results": "58", "hashOfConfig": "31"}, {"size": 11044, "mtime": 1752701534378, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1m3d8d5", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Kimi\\src\\index.tsx", [], [], "E:\\Kimi\\src\\App.tsx", [], [], "E:\\Kimi\\src\\store\\store.ts", [], [], "E:\\Kimi\\src\\i18n\\i18n.ts", [], [], "E:\\Kimi\\src\\contexts\\I18nContext.tsx", [], [], "E:\\Kimi\\src\\store\\slices\\companySlice.ts", [], [], "E:\\Kimi\\src\\store\\slices\\authSlice.ts", [], [], "E:\\Kimi\\src\\store\\slices\\accountsSlice.ts", [], [], "E:\\Kimi\\src\\store\\slices\\customersSlice.ts", [], [], "E:\\Kimi\\src\\store\\slices\\suppliersSlice.ts", [], [], "E:\\Kimi\\src\\store\\slices\\productsSlice.ts", [], [], "E:\\Kimi\\src\\store\\slices\\invoicesSlice.ts", [], [], "E:\\Kimi\\src\\components\\Layout\\Layout.tsx", [], [], "E:\\Kimi\\src\\pages\\Auth\\LoginPage.tsx", [], [], "E:\\Kimi\\src\\pages\\Accounting\\ChartOfAccounts.tsx", [], [], "E:\\Kimi\\src\\pages\\Dashboard\\Dashboard.tsx", [], [], "E:\\Kimi\\src\\pages\\CRM\\Suppliers.tsx", [], [], "E:\\Kimi\\src\\pages\\Customers\\CustomersPage.tsx", [], [], "E:\\Kimi\\src\\pages\\Inventory\\Products.tsx", [], [], "E:\\Kimi\\src\\pages\\Sales\\Invoices.tsx", [], [], "E:\\Kimi\\src\\pages\\Purchases\\Purchases.tsx", [], [], "E:\\Kimi\\src\\pages\\Finance\\Payments.tsx", [], [], "E:\\Kimi\\src\\pages\\HR\\Employees.tsx", [], [], "E:\\Kimi\\src\\pages\\HR\\Payroll.tsx", [], [], "E:\\Kimi\\src\\pages\\Assets\\FixedAssets.tsx", [], [], "E:\\Kimi\\src\\pages\\Reports\\Reports.tsx", [], [], "E:\\Kimi\\src\\pages\\Settings\\Settings.tsx", [], [], "E:\\Kimi\\src\\api\\database.ts", [], [], "E:\\Kimi\\src\\components\\Layout\\Sidebar.tsx", [], []]