// Database API layer for Electron app
// This file handles all database operations using IPC communication with the main process

export interface DatabaseResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// Helper function to handle errors
const handleError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return 'حدث خطأ غير متوقع';
};

// Helper function to execute database queries safely
const executeQuery = async (query: string, params?: any[]): Promise<any> => {
  return await window.electronAPI.invoke('db-query', {
    query,
    params
  });
};

// Company API
export const companyAPI = {
  async getCompany(): Promise<DatabaseResponse> {
    try {
      const result = await executeQuery('SELECT * FROM companies LIMIT 1');
      return { success: true, data: result[0] };
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  },

  async updateCompany(company: any): Promise<DatabaseResponse> {
    try {
      const result = await executeQuery(`UPDATE companies SET 
        name = ?, address = ?, phone = ?, email = ?, 
        tax_number = ?, commercial_register = ?, currency = ?
        WHERE id = ?`, [
        company.name, company.address, company.phone, company.email,
        company.tax_number, company.commercial_register, company.currency,
        company.id
      ]);
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  }
};

// Customers API
export const customersAPI = {
  async getCustomers(): Promise<DatabaseResponse> {
    try {
      const result = await executeQuery('SELECT * FROM customers ORDER BY name');
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  },

  async getCustomer(id: number): Promise<DatabaseResponse> {
    try {
      const result = await executeQuery('SELECT * FROM customers WHERE id = ?', [id]);
      return { success: true, data: result[0] };
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  },

  async createCustomer(customer: any): Promise<DatabaseResponse> {
    try {
      const result = await executeQuery(`INSERT INTO customers (
        name, email, phone, address, tax_number, 
        credit_limit, payment_terms, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [
        customer.name, customer.email, customer.phone, customer.address,
        customer.tax_number, customer.credit_limit, customer.payment_terms,
        customer.status || 'active'
      ]);
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  },

  async updateCustomer(id: number, customer: any): Promise<DatabaseResponse> {
    try {
      const result = await executeQuery(`UPDATE customers SET 
        name = ?, email = ?, phone = ?, address = ?, 
        tax_number = ?, credit_limit = ?, payment_terms = ?, status = ?
        WHERE id = ?`, [
        customer.name, customer.email, customer.phone, customer.address,
        customer.tax_number, customer.credit_limit, customer.payment_terms,
        customer.status, id
      ]);
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  },

  async deleteCustomer(id: number): Promise<DatabaseResponse> {
    try {
      const result = await executeQuery('DELETE FROM customers WHERE id = ?', [id]);
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  }
};

// Authentication API
export const authAPI = {
  async login(credentials: { username: string; password: string }): Promise<DatabaseResponse> {
    try {
      const result = await executeQuery(
        'SELECT * FROM users WHERE (username = ? OR email = ?) AND password = ? AND status = "active"',
        [credentials.username, credentials.username, credentials.password]
      );
      
      if (result.length > 0) {
        const user = result[0];
        // Remove password from response
        delete user.password;
        return { success: true, data: user };
      } else {
        return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
      }
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  },

  async logout(): Promise<DatabaseResponse> {
    try {
      // Clear any session data if needed
      return { success: true };
    } catch (error) {
      return { success: false, error: handleError(error) };
    }
  }
};

// Combined API object for backward compatibility
export const dbAPI = {
  company: companyAPI,
  customers: customersAPI,
  auth: authAPI
};

// Initialize database with default data
export const initializeDatabase = async (): Promise<DatabaseResponse> => {
  try {
    // Check if company exists, if not create default
    const companyResult = await companyAPI.getCompany();
    if (!companyResult.success || !companyResult.data) {
      await executeQuery(`INSERT INTO companies (
        name, address, phone, email, tax_number, 
        commercial_register, currency
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`, [
        'شركة المحاسبة المتقدمة',
        'الرياض، المملكة العربية السعودية',
        '+966501234567',
        '<EMAIL>',
        '123456789012345',
        '1010123456',
        'SAR'
      ]);
    }

    // Check if admin user exists, if not create default
    const userResult = await executeQuery('SELECT COUNT(*) as count FROM users');
    
    if (userResult[0].count === 0) {
      await executeQuery(`INSERT INTO users (
        name, email, password, role, status
      ) VALUES (?, ?, ?, ?, ?)`, [
        'المدير العام',
        '<EMAIL>',
        'admin123', // In production, this should be hashed
        'admin',
        'active'
      ]);
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: handleError(error) };
  }
};
