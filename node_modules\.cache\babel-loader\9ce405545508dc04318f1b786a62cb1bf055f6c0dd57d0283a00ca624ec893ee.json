{"ast": null, "code": "import isSameDay from \"../isSameDay/index.js\";\nimport subDays from \"../subDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is yesterday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\nexport default function isYesterday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, subDays(Date.now(), 1));\n}", "map": {"version": 3, "names": ["isSameDay", "subDays", "requiredArgs", "isYesterday", "dirtyDate", "arguments", "Date", "now"], "sources": ["E:/Kimi/node_modules/date-fns/esm/isYesterday/index.js"], "sourcesContent": ["import isSameDay from \"../isSameDay/index.js\";\nimport subDays from \"../subDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is yesterday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\nexport default function isYesterday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, subDays(Date.now(), 1));\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,SAAS,EAAE;EAC7CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOL,SAAS,CAACI,SAAS,EAAEH,OAAO,CAACK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}