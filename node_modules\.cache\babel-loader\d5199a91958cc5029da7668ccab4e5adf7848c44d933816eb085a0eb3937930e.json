{"ast": null, "code": "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "map": {"version": 3, "names": ["getHTMLElementScroll", "element", "scrollLeft", "scrollTop"], "sources": ["E:/Kimi/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js"], "sourcesContent": ["export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,oBAAoBA,CAACC,OAAO,EAAE;EACpD,OAAO;IACLC,UAAU,EAAED,OAAO,CAACC,UAAU;IAC9BC,SAAS,EAAEF,OAAO,CAACE;EACrB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}