{"ast": null, "code": "import PropTypes from 'prop-types';\nexport var timeoutsShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n  enter: PropTypes.number,\n  exit: PropTypes.number,\n  appear: PropTypes.number\n}).isRequired]) : null;\nexport var classNamesShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.string, PropTypes.shape({\n  enter: PropTypes.string,\n  exit: PropTypes.string,\n  active: PropTypes.string\n}), PropTypes.shape({\n  enter: PropTypes.string,\n  enterDone: PropTypes.string,\n  enterActive: PropTypes.string,\n  exit: PropTypes.string,\n  exitDone: PropTypes.string,\n  exitActive: PropTypes.string\n})]) : null;", "map": {"version": 3, "names": ["PropTypes", "timeoutsShape", "process", "env", "NODE_ENV", "oneOfType", "number", "shape", "enter", "exit", "appear", "isRequired", "classNamesShape", "string", "active", "enterDone", "enterActive", "exitDone", "exitActive"], "sources": ["E:/Kimi/node_modules/react-transition-group/esm/utils/PropTypes.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nexport var timeoutsShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n  enter: PropTypes.number,\n  exit: PropTypes.number,\n  appear: PropTypes.number\n}).isRequired]) : null;\nexport var classNamesShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.string, PropTypes.shape({\n  enter: PropTypes.string,\n  exit: PropTypes.string,\n  active: PropTypes.string\n}), PropTypes.shape({\n  enter: PropTypes.string,\n  enterDone: PropTypes.string,\n  enterActive: PropTypes.string,\n  exit: PropTypes.string,\n  exitDone: PropTypes.string,\n  exitActive: PropTypes.string\n})]) : null;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAO,IAAIC,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,SAAS,CAACK,SAAS,CAAC,CAACL,SAAS,CAACM,MAAM,EAAEN,SAAS,CAACO,KAAK,CAAC;EACxHC,KAAK,EAAER,SAAS,CAACM,MAAM;EACvBG,IAAI,EAAET,SAAS,CAACM,MAAM;EACtBI,MAAM,EAAEV,SAAS,CAACM;AACpB,CAAC,CAAC,CAACK,UAAU,CAAC,CAAC,GAAG,IAAI;AACtB,OAAO,IAAIC,eAAe,GAAGV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,SAAS,CAACK,SAAS,CAAC,CAACL,SAAS,CAACa,MAAM,EAAEb,SAAS,CAACO,KAAK,CAAC;EAC1HC,KAAK,EAAER,SAAS,CAACa,MAAM;EACvBJ,IAAI,EAAET,SAAS,CAACa,MAAM;EACtBC,MAAM,EAAEd,SAAS,CAACa;AACpB,CAAC,CAAC,EAAEb,SAAS,CAACO,KAAK,CAAC;EAClBC,KAAK,EAAER,SAAS,CAACa,MAAM;EACvBE,SAAS,EAAEf,SAAS,CAACa,MAAM;EAC3BG,WAAW,EAAEhB,SAAS,CAACa,MAAM;EAC7BJ,IAAI,EAAET,SAAS,CAACa,MAAM;EACtBI,QAAQ,EAAEjB,SAAS,CAACa,MAAM;EAC1BK,UAAU,EAAElB,SAAS,CAACa;AACxB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}