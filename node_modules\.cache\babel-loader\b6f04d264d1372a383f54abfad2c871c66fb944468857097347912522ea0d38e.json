{"ast": null, "code": "function areArraysEqual(array1, array2, itemComparer = (a, b) => a === b) {\n  return array1.length === array2.length && array1.every((value, index) => itemComparer(value, array2[index]));\n}\nexport default areArraysEqual;", "map": {"version": 3, "names": ["areArraysEqual", "array1", "array2", "itemComparer", "a", "b", "length", "every", "value", "index"], "sources": ["E:/Kimi/node_modules/@mui/material/utils/areArraysEqual.js"], "sourcesContent": ["function areArraysEqual(array1, array2, itemComparer = (a, b) => a === b) {\n  return array1.length === array2.length && array1.every((value, index) => itemComparer(value, array2[index]));\n}\nexport default areArraysEqual;"], "mappings": "AAAA,SAASA,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAEC,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,EAAE;EACxE,OAAOJ,MAAM,CAACK,MAAM,KAAKJ,MAAM,CAACI,MAAM,IAAIL,MAAM,CAACM,KAAK,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKN,YAAY,CAACK,KAAK,EAAEN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC;AAC9G;AACA,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}