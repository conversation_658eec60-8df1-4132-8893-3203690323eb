{"ast": null, "code": "var _jsxFileName = \"E:\\\\Kimi\\\\src\\\\contexts\\\\I18nContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst I18nContext = /*#__PURE__*/createContext(undefined);\nexport const I18nProvider = ({\n  children\n}) => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const [language, setLanguageState] = useState(() => {\n    return localStorage.getItem('language') || 'ar';\n  });\n  const setLanguage = lang => {\n    setLanguageState(lang);\n    i18n.changeLanguage(lang);\n    localStorage.setItem('language', lang);\n\n    // Update document direction\n    document.dir = lang === 'ar' ? 'rtl' : 'ltr';\n    document.documentElement.lang = lang;\n\n    // Update body class for styling\n    document.body.className = lang === 'ar' ? 'rtl' : 'ltr';\n  };\n  useEffect(() => {\n    i18n.changeLanguage(language);\n    document.dir = language === 'ar' ? 'rtl' : 'ltr';\n    document.documentElement.lang = language;\n    document.body.className = language === 'ar' ? 'rtl' : 'ltr';\n  }, [language, i18n]);\n  const isRTL = language === 'ar';\n  const value = {\n    language,\n    setLanguage,\n    t,\n    isRTL\n  };\n  return /*#__PURE__*/_jsxDEV(I18nContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(I18nProvider, \"RBPrUFN+luRJpZlm7Hs2M3uMyOQ=\", false, function () {\n  return [useTranslation];\n});\n_c = I18nProvider;\nexport const useI18n = () => {\n  _s2();\n  const context = useContext(I18nContext);\n  if (context === undefined) {\n    throw new Error('useI18n must be used within an I18nProvider');\n  }\n  return context;\n};\n_s2(useI18n, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"I18nProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "useTranslation", "jsxDEV", "_jsxDEV", "I18nContext", "undefined", "I18nProvider", "children", "_s", "t", "i18n", "language", "setLanguageState", "localStorage", "getItem", "setLanguage", "lang", "changeLanguage", "setItem", "document", "dir", "documentElement", "body", "className", "isRTL", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useI18n", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["E:/Kimi/src/contexts/I18nContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface I18nContextType {\r\n  language: string;\r\n  setLanguage: (lang: string) => void;\r\n  t: (key: string, options?: any) => any;\r\n  isRTL: boolean;\r\n}\r\n\r\nconst I18nContext = createContext<I18nContextType | undefined>(undefined);\r\n\r\ninterface I18nProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {\r\n  const { t, i18n } = useTranslation();\r\n  const [language, setLanguageState] = useState<string>(() => {\r\n    return localStorage.getItem('language') || 'ar';\r\n  });\r\n\r\n  const setLanguage = (lang: string) => {\r\n    setLanguageState(lang);\r\n    i18n.changeLanguage(lang);\r\n    localStorage.setItem('language', lang);\r\n\r\n    // Update document direction\r\n    document.dir = lang === 'ar' ? 'rtl' : 'ltr';\r\n    document.documentElement.lang = lang;\r\n\r\n    // Update body class for styling\r\n    document.body.className = lang === 'ar' ? 'rtl' : 'ltr';\r\n  };\r\n\r\n  useEffect(() => {\r\n    i18n.changeLanguage(language);\r\n    document.dir = language === 'ar' ? 'rtl' : 'ltr';\r\n    document.documentElement.lang = language;\r\n    document.body.className = language === 'ar' ? 'rtl' : 'ltr';\r\n  }, [language, i18n]);\r\n\r\n  const isRTL = language === 'ar';\r\n\r\n  const value: I18nContextType = {\r\n    language,\r\n    setLanguage,\r\n    t,\r\n    isRTL,\r\n  };\r\n\r\n  return (\r\n    <I18nContext.Provider value={value}>\r\n      {children}\r\n    </I18nContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useI18n = (): I18nContextType => {\r\n  const context = useContext(I18nContext);\r\n  if (context === undefined) {\r\n    throw new Error('useI18n must be used within an I18nProvider');\r\n  }\r\n  return context;\r\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS/C,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAMzE,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGT,cAAc,CAAC,CAAC;EACpC,MAAM,CAACU,QAAQ,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAS,MAAM;IAC1D,OAAOc,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;EACjD,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAIC,IAAY,IAAK;IACpCJ,gBAAgB,CAACI,IAAI,CAAC;IACtBN,IAAI,CAACO,cAAc,CAACD,IAAI,CAAC;IACzBH,YAAY,CAACK,OAAO,CAAC,UAAU,EAAEF,IAAI,CAAC;;IAEtC;IACAG,QAAQ,CAACC,GAAG,GAAGJ,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK;IAC5CG,QAAQ,CAACE,eAAe,CAACL,IAAI,GAAGA,IAAI;;IAEpC;IACAG,QAAQ,CAACG,IAAI,CAACC,SAAS,GAAGP,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK;EACzD,CAAC;EAEDhB,SAAS,CAAC,MAAM;IACdU,IAAI,CAACO,cAAc,CAACN,QAAQ,CAAC;IAC7BQ,QAAQ,CAACC,GAAG,GAAGT,QAAQ,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK;IAChDQ,QAAQ,CAACE,eAAe,CAACL,IAAI,GAAGL,QAAQ;IACxCQ,QAAQ,CAACG,IAAI,CAACC,SAAS,GAAGZ,QAAQ,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK;EAC7D,CAAC,EAAE,CAACA,QAAQ,EAAED,IAAI,CAAC,CAAC;EAEpB,MAAMc,KAAK,GAAGb,QAAQ,KAAK,IAAI;EAE/B,MAAMc,KAAsB,GAAG;IAC7Bd,QAAQ;IACRI,WAAW;IACXN,CAAC;IACDe;EACF,CAAC;EAED,oBACErB,OAAA,CAACC,WAAW,CAACsB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAlB,QAAA,EAChCA;EAAQ;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACtB,EAAA,CAxCWF,YAAyC;EAAA,QAChCL,cAAc;AAAA;AAAA8B,EAAA,GADvBzB,YAAyC;AA0CtD,OAAO,MAAM0B,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGpC,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI8B,OAAO,KAAK7B,SAAS,EAAE;IACzB,MAAM,IAAI8B,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}