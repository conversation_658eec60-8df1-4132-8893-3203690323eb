{"ast": null, "code": "import differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport startOfWeekYear from \"../startOfWeekYear/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name setWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Set the local week-numbering year to the given date.\n *\n * @description\n * Set the local week-numbering year to the given date,\n * saving the week number and the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} weekYear - the local week-numbering year of the new date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Date} the new date with the local week-numbering year set\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010 with default options:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004)\n * //=> Sat Jan 03 2004 00:00:00\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010,\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport default function setWeekYear(dirtyDate, dirtyWeekYear, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n  var date = toDate(dirtyDate);\n  var weekYear = toInteger(dirtyWeekYear);\n  var diff = differenceInCalendarDays(date, startOfWeekYear(date, options));\n  var firstWeek = new Date(0);\n  firstWeek.setFullYear(weekYear, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  date = startOfWeekYear(firstWeek, options);\n  date.setDate(date.getDate() + diff);\n  return date;\n}", "map": {"version": 3, "names": ["differenceInCalendarDays", "startOfWeekYear", "toDate", "toInteger", "requiredArgs", "getDefaultOptions", "setWeekYear", "dirtyDate", "dirtyWeekYear", "options", "_ref", "_ref2", "_ref3", "_options$firstWeekCon", "_options$locale", "_options$locale$optio", "_defaultOptions$local", "_defaultOptions$local2", "arguments", "defaultOptions", "firstWeekContainsDate", "locale", "date", "weekYear", "diff", "firstWeek", "Date", "setFullYear", "setHours", "setDate", "getDate"], "sources": ["E:/Kimi/node_modules/date-fns/esm/setWeekYear/index.js"], "sourcesContent": ["import differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport startOfWeekYear from \"../startOfWeekYear/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name setWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Set the local week-numbering year to the given date.\n *\n * @description\n * Set the local week-numbering year to the given date,\n * saving the week number and the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} weekYear - the local week-numbering year of the new date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Date} the new date with the local week-numbering year set\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010 with default options:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004)\n * //=> Sat Jan 03 2004 00:00:00\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010,\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport default function setWeekYear(dirtyDate, dirtyWeekYear, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n  var date = toDate(dirtyDate);\n  var weekYear = toInteger(dirtyWeekYear);\n  var diff = differenceInCalendarDays(date, startOfWeekYear(date, options));\n  var firstWeek = new Date(0);\n  firstWeek.setFullYear(weekYear, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  date = startOfWeekYear(firstWeek, options);\n  date.setDate(date.getDate() + diff);\n  return date;\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,SAAS,EAAEC,aAAa,EAAEC,OAAO,EAAE;EACrE,IAAIC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB;EACpIb,YAAY,CAAC,CAAC,EAAEc,SAAS,CAAC;EAC1B,IAAIC,cAAc,GAAGd,iBAAiB,CAAC,CAAC;EACxC,IAAIe,qBAAqB,GAAGjB,SAAS,CAAC,CAACO,IAAI,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,qBAAqB,MAAM,IAAI,IAAIP,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACK,eAAe,GAAGL,OAAO,CAACY,MAAM,MAAM,IAAI,IAAIP,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,eAAe,CAACL,OAAO,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACK,qBAAqB,MAAM,IAAI,IAAIR,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGO,cAAc,CAACC,qBAAqB,MAAM,IAAI,IAAIT,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,qBAAqB,GAAGG,cAAc,CAACE,MAAM,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACP,OAAO,MAAM,IAAI,IAAIQ,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACG,qBAAqB,MAAM,IAAI,IAAIV,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;EACl7B,IAAIY,IAAI,GAAGpB,MAAM,CAACK,SAAS,CAAC;EAC5B,IAAIgB,QAAQ,GAAGpB,SAAS,CAACK,aAAa,CAAC;EACvC,IAAIgB,IAAI,GAAGxB,wBAAwB,CAACsB,IAAI,EAAErB,eAAe,CAACqB,IAAI,EAAEb,OAAO,CAAC,CAAC;EACzE,IAAIgB,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC3BD,SAAS,CAACE,WAAW,CAACJ,QAAQ,EAAE,CAAC,EAAEH,qBAAqB,CAAC;EACzDK,SAAS,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9BN,IAAI,GAAGrB,eAAe,CAACwB,SAAS,EAAEhB,OAAO,CAAC;EAC1Ca,IAAI,CAACO,OAAO,CAACP,IAAI,CAACQ,OAAO,CAAC,CAAC,GAAGN,IAAI,CAAC;EACnC,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}