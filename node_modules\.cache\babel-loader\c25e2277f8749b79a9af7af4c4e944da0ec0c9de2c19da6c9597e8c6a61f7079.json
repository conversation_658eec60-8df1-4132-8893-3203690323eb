{"ast": null, "code": "// Database API layer for Electron app\n// This file handles all database operations using IPC communication with the main process\n\n// Helper function to handle errors\nconst handleError = error => {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return 'حدث خطأ غير متوقع';\n};\n\n// Helper function to execute database queries safely\nconst executeQuery = async (query, params) => {\n  return await window.electronAPI.invoke('db-query', {\n    query,\n    params\n  });\n};\n\n// Company API\nexport const companyAPI = {\n  async getCompany() {\n    try {\n      const result = await executeQuery('SELECT * FROM companies LIMIT 1');\n      return {\n        success: true,\n        data: result[0]\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  },\n  async updateCompany(company) {\n    try {\n      const result = await executeQuery(`UPDATE companies SET \n        name = ?, address = ?, phone = ?, email = ?, \n        tax_number = ?, commercial_register = ?, currency = ?\n        WHERE id = ?`, [company.name, company.address, company.phone, company.email, company.tax_number, company.commercial_register, company.currency, company.id]);\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  }\n};\n\n// Customers API\nexport const customersAPI = {\n  async getCustomers() {\n    try {\n      const result = await executeQuery('SELECT * FROM customers ORDER BY name');\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  },\n  async getCustomer(id) {\n    try {\n      const result = await executeQuery('SELECT * FROM customers WHERE id = ?', [id]);\n      return {\n        success: true,\n        data: result[0]\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  },\n  async createCustomer(customer) {\n    try {\n      const result = await executeQuery(`INSERT INTO customers (\n        name, email, phone, address, tax_number, \n        credit_limit, payment_terms, status\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [customer.name, customer.email, customer.phone, customer.address, customer.tax_number, customer.credit_limit, customer.payment_terms, customer.status || 'active']);\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  },\n  async updateCustomer(id, customer) {\n    try {\n      const result = await executeQuery(`UPDATE customers SET \n        name = ?, email = ?, phone = ?, address = ?, \n        tax_number = ?, credit_limit = ?, payment_terms = ?, status = ?\n        WHERE id = ?`, [customer.name, customer.email, customer.phone, customer.address, customer.tax_number, customer.credit_limit, customer.payment_terms, customer.status, id]);\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  },\n  async deleteCustomer(id) {\n    try {\n      const result = await executeQuery('DELETE FROM customers WHERE id = ?', [id]);\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  }\n};\n\n// Authentication API\nexport const authAPI = {\n  async login(credentials) {\n    try {\n      const result = await executeQuery('SELECT * FROM users WHERE (username = ? OR email = ?) AND password = ? AND status = \"active\"', [credentials.username, credentials.username, credentials.password]);\n      if (result.length > 0) {\n        const user = result[0];\n        // Remove password from response\n        delete user.password;\n        return {\n          success: true,\n          data: user\n        };\n      } else {\n        return {\n          success: false,\n          error: 'اسم المستخدم أو كلمة المرور غير صحيحة'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  },\n  async logout() {\n    try {\n      // Clear any session data if needed\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  }\n};\n\n// Combined API object for backward compatibility\nexport const dbAPI = {\n  company: companyAPI,\n  customers: customersAPI,\n  auth: authAPI\n};\n\n// Initialize database with default data\nexport const initializeDatabase = async () => {\n  try {\n    // Check if company exists, if not create default\n    const companyResult = await companyAPI.getCompany();\n    if (!companyResult.success || !companyResult.data) {\n      await executeQuery(`INSERT INTO companies (\n        name, address, phone, email, tax_number, \n        commercial_register, currency\n      ) VALUES (?, ?, ?, ?, ?, ?, ?)`, ['شركة المحاسبة المتقدمة', 'الرياض، المملكة العربية السعودية', '+966501234567', '<EMAIL>', '123456789012345', '1010123456', 'SAR']);\n    }\n\n    // Check if admin user exists, if not create default\n    const userResult = await executeQuery('SELECT COUNT(*) as count FROM users');\n    if (userResult[0].count === 0) {\n      await executeQuery(`INSERT INTO users (\n        name, email, password, role, status\n      ) VALUES (?, ?, ?, ?, ?)`, ['المدير العام', '<EMAIL>', 'admin123',\n      // In production, this should be hashed\n      'admin', 'active']);\n    }\n    return {\n      success: true\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: handleError(error)\n    };\n  }\n};", "map": {"version": 3, "names": ["handleError", "error", "Error", "message", "execute<PERSON>uery", "query", "params", "window", "electronAPI", "invoke", "companyAPI", "getCompany", "result", "success", "data", "updateCompany", "company", "name", "address", "phone", "email", "tax_number", "commercial_register", "currency", "id", "customersAPI", "getCustomers", "getCustomer", "createCustomer", "customer", "credit_limit", "payment_terms", "status", "updateCustomer", "deleteCustomer", "authAPI", "login", "credentials", "username", "password", "length", "user", "logout", "dbAPI", "customers", "auth", "initializeDatabase", "companyResult", "userResult", "count"], "sources": ["E:/Kimi/src/api/database.ts"], "sourcesContent": ["// Database API layer for Electron app\n// This file handles all database operations using IPC communication with the main process\n\nexport interface DatabaseResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n}\n\n// Helper function to handle errors\nconst handleError = (error: unknown): string => {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return 'حدث خطأ غير متوقع';\n};\n\n// Helper function to execute database queries safely\nconst executeQuery = async (query: string, params?: any[]): Promise<any> => {\n  return await window.electronAPI.invoke('db-query', {\n    query,\n    params\n  });\n};\n\n// Company API\nexport const companyAPI = {\n  async getCompany(): Promise<DatabaseResponse> {\n    try {\n      const result = await executeQuery('SELECT * FROM companies LIMIT 1');\n      return { success: true, data: result[0] };\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  },\n\n  async updateCompany(company: any): Promise<DatabaseResponse> {\n    try {\n      const result = await executeQuery(`UPDATE companies SET \n        name = ?, address = ?, phone = ?, email = ?, \n        tax_number = ?, commercial_register = ?, currency = ?\n        WHERE id = ?`, [\n        company.name, company.address, company.phone, company.email,\n        company.tax_number, company.commercial_register, company.currency,\n        company.id\n      ]);\n      return { success: true, data: result };\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  }\n};\n\n// Customers API\nexport const customersAPI = {\n  async getCustomers(): Promise<DatabaseResponse> {\n    try {\n      const result = await executeQuery('SELECT * FROM customers ORDER BY name');\n      return { success: true, data: result };\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  },\n\n  async getCustomer(id: number): Promise<DatabaseResponse> {\n    try {\n      const result = await executeQuery('SELECT * FROM customers WHERE id = ?', [id]);\n      return { success: true, data: result[0] };\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  },\n\n  async createCustomer(customer: any): Promise<DatabaseResponse> {\n    try {\n      const result = await executeQuery(`INSERT INTO customers (\n        name, email, phone, address, tax_number, \n        credit_limit, payment_terms, status\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [\n        customer.name, customer.email, customer.phone, customer.address,\n        customer.tax_number, customer.credit_limit, customer.payment_terms,\n        customer.status || 'active'\n      ]);\n      return { success: true, data: result };\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  },\n\n  async updateCustomer(id: number, customer: any): Promise<DatabaseResponse> {\n    try {\n      const result = await executeQuery(`UPDATE customers SET \n        name = ?, email = ?, phone = ?, address = ?, \n        tax_number = ?, credit_limit = ?, payment_terms = ?, status = ?\n        WHERE id = ?`, [\n        customer.name, customer.email, customer.phone, customer.address,\n        customer.tax_number, customer.credit_limit, customer.payment_terms,\n        customer.status, id\n      ]);\n      return { success: true, data: result };\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  },\n\n  async deleteCustomer(id: number): Promise<DatabaseResponse> {\n    try {\n      const result = await executeQuery('DELETE FROM customers WHERE id = ?', [id]);\n      return { success: true, data: result };\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  }\n};\n\n// Authentication API\nexport const authAPI = {\n  async login(credentials: { username: string; password: string }): Promise<DatabaseResponse> {\n    try {\n      const result = await executeQuery(\n        'SELECT * FROM users WHERE (username = ? OR email = ?) AND password = ? AND status = \"active\"',\n        [credentials.username, credentials.username, credentials.password]\n      );\n      \n      if (result.length > 0) {\n        const user = result[0];\n        // Remove password from response\n        delete user.password;\n        return { success: true, data: user };\n      } else {\n        return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' };\n      }\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  },\n\n  async logout(): Promise<DatabaseResponse> {\n    try {\n      // Clear any session data if needed\n      return { success: true };\n    } catch (error) {\n      return { success: false, error: handleError(error) };\n    }\n  }\n};\n\n// Combined API object for backward compatibility\nexport const dbAPI = {\n  company: companyAPI,\n  customers: customersAPI,\n  auth: authAPI\n};\n\n// Initialize database with default data\nexport const initializeDatabase = async (): Promise<DatabaseResponse> => {\n  try {\n    // Check if company exists, if not create default\n    const companyResult = await companyAPI.getCompany();\n    if (!companyResult.success || !companyResult.data) {\n      await executeQuery(`INSERT INTO companies (\n        name, address, phone, email, tax_number, \n        commercial_register, currency\n      ) VALUES (?, ?, ?, ?, ?, ?, ?)`, [\n        'شركة المحاسبة المتقدمة',\n        'الرياض، المملكة العربية السعودية',\n        '+966501234567',\n        '<EMAIL>',\n        '123456789012345',\n        '1010123456',\n        'SAR'\n      ]);\n    }\n\n    // Check if admin user exists, if not create default\n    const userResult = await executeQuery('SELECT COUNT(*) as count FROM users');\n    \n    if (userResult[0].count === 0) {\n      await executeQuery(`INSERT INTO users (\n        name, email, password, role, status\n      ) VALUES (?, ?, ?, ?, ?)`, [\n        'المدير العام',\n        '<EMAIL>',\n        'admin123', // In production, this should be hashed\n        'admin',\n        'active'\n      ]);\n    }\n\n    return { success: true };\n  } catch (error) {\n    return { success: false, error: handleError(error) };\n  }\n};\n"], "mappings": "AAAA;AACA;;AAQA;AACA,MAAMA,WAAW,GAAIC,KAAc,IAAa;EAC9C,IAAIA,KAAK,YAAYC,KAAK,EAAE;IAC1B,OAAOD,KAAK,CAACE,OAAO;EACtB;EACA,OAAO,mBAAmB;AAC5B,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG,MAAAA,CAAOC,KAAa,EAAEC,MAAc,KAAmB;EAC1E,OAAO,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;IACjDJ,KAAK;IACLC;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMI,UAAU,GAAG;EACxB,MAAMC,UAAUA,CAAA,EAA8B;IAC5C,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMR,YAAY,CAAC,iCAAiC,CAAC;MACpE,OAAO;QAAES,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEF,MAAM,CAAC,CAAC;MAAE,CAAC;IAC3C,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAMc,aAAaA,CAACC,OAAY,EAA6B;IAC3D,IAAI;MACF,MAAMJ,MAAM,GAAG,MAAMR,YAAY,CAAC;AACxC;AACA;AACA,qBAAqB,EAAE,CACfY,OAAO,CAACC,IAAI,EAAED,OAAO,CAACE,OAAO,EAAEF,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACI,KAAK,EAC3DJ,OAAO,CAACK,UAAU,EAAEL,OAAO,CAACM,mBAAmB,EAAEN,OAAO,CAACO,QAAQ,EACjEP,OAAO,CAACQ,EAAE,CACX,CAAC;MACF,OAAO;QAAEX,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEF;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMwB,YAAY,GAAG;EAC1B,MAAMC,YAAYA,CAAA,EAA8B;IAC9C,IAAI;MACF,MAAMd,MAAM,GAAG,MAAMR,YAAY,CAAC,uCAAuC,CAAC;MAC1E,OAAO;QAAES,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEF;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAM0B,WAAWA,CAACH,EAAU,EAA6B;IACvD,IAAI;MACF,MAAMZ,MAAM,GAAG,MAAMR,YAAY,CAAC,sCAAsC,EAAE,CAACoB,EAAE,CAAC,CAAC;MAC/E,OAAO;QAAEX,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEF,MAAM,CAAC,CAAC;MAAE,CAAC;IAC3C,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAM2B,cAAcA,CAACC,QAAa,EAA6B;IAC7D,IAAI;MACF,MAAMjB,MAAM,GAAG,MAAMR,YAAY,CAAC;AACxC;AACA;AACA,wCAAwC,EAAE,CAClCyB,QAAQ,CAACZ,IAAI,EAAEY,QAAQ,CAACT,KAAK,EAAES,QAAQ,CAACV,KAAK,EAAEU,QAAQ,CAACX,OAAO,EAC/DW,QAAQ,CAACR,UAAU,EAAEQ,QAAQ,CAACC,YAAY,EAAED,QAAQ,CAACE,aAAa,EAClEF,QAAQ,CAACG,MAAM,IAAI,QAAQ,CAC5B,CAAC;MACF,OAAO;QAAEnB,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEF;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAMgC,cAAcA,CAACT,EAAU,EAAEK,QAAa,EAA6B;IACzE,IAAI;MACF,MAAMjB,MAAM,GAAG,MAAMR,YAAY,CAAC;AACxC;AACA;AACA,qBAAqB,EAAE,CACfyB,QAAQ,CAACZ,IAAI,EAAEY,QAAQ,CAACT,KAAK,EAAES,QAAQ,CAACV,KAAK,EAAEU,QAAQ,CAACX,OAAO,EAC/DW,QAAQ,CAACR,UAAU,EAAEQ,QAAQ,CAACC,YAAY,EAAED,QAAQ,CAACE,aAAa,EAClEF,QAAQ,CAACG,MAAM,EAAER,EAAE,CACpB,CAAC;MACF,OAAO;QAAEX,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEF;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAMiC,cAAcA,CAACV,EAAU,EAA6B;IAC1D,IAAI;MACF,MAAMZ,MAAM,GAAG,MAAMR,YAAY,CAAC,oCAAoC,EAAE,CAACoB,EAAE,CAAC,CAAC;MAC7E,OAAO;QAAEX,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEF;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMkC,OAAO,GAAG;EACrB,MAAMC,KAAKA,CAACC,WAAmD,EAA6B;IAC1F,IAAI;MACF,MAAMzB,MAAM,GAAG,MAAMR,YAAY,CAC/B,8FAA8F,EAC9F,CAACiC,WAAW,CAACC,QAAQ,EAAED,WAAW,CAACC,QAAQ,EAAED,WAAW,CAACE,QAAQ,CACnE,CAAC;MAED,IAAI3B,MAAM,CAAC4B,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMC,IAAI,GAAG7B,MAAM,CAAC,CAAC,CAAC;QACtB;QACA,OAAO6B,IAAI,CAACF,QAAQ;QACpB,OAAO;UAAE1B,OAAO,EAAE,IAAI;UAAEC,IAAI,EAAE2B;QAAK,CAAC;MACtC,CAAC,MAAM;QACL,OAAO;UAAE5B,OAAO,EAAE,KAAK;UAAEZ,KAAK,EAAE;QAAwC,CAAC;MAC3E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAMyC,MAAMA,CAAA,EAA8B;IACxC,IAAI;MACF;MACA,OAAO;QAAE7B,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,OAAO;QAAEY,OAAO,EAAE,KAAK;QAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAM0C,KAAK,GAAG;EACnB3B,OAAO,EAAEN,UAAU;EACnBkC,SAAS,EAAEnB,YAAY;EACvBoB,IAAI,EAAEV;AACR,CAAC;;AAED;AACA,OAAO,MAAMW,kBAAkB,GAAG,MAAAA,CAAA,KAAuC;EACvE,IAAI;IACF;IACA,MAAMC,aAAa,GAAG,MAAMrC,UAAU,CAACC,UAAU,CAAC,CAAC;IACnD,IAAI,CAACoC,aAAa,CAAClC,OAAO,IAAI,CAACkC,aAAa,CAACjC,IAAI,EAAE;MACjD,MAAMV,YAAY,CAAC;AACzB;AACA;AACA,qCAAqC,EAAE,CAC/B,wBAAwB,EACxB,kCAAkC,EAClC,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,KAAK,CACN,CAAC;IACJ;;IAEA;IACA,MAAM4C,UAAU,GAAG,MAAM5C,YAAY,CAAC,qCAAqC,CAAC;IAE5E,IAAI4C,UAAU,CAAC,CAAC,CAAC,CAACC,KAAK,KAAK,CAAC,EAAE;MAC7B,MAAM7C,YAAY,CAAC;AACzB;AACA,+BAA+B,EAAE,CACzB,cAAc,EACd,mBAAmB,EACnB,UAAU;MAAE;MACZ,OAAO,EACP,QAAQ,CACT,CAAC;IACJ;IAEA,OAAO;MAAES,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACd,OAAO;MAAEY,OAAO,EAAE,KAAK;MAAEZ,KAAK,EAAED,WAAW,CAACC,KAAK;IAAE,CAAC;EACtD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}