{"ast": null, "code": "// Database API layer for Electron app\n// This file handles all database operations using IPC communication with the main process\n\n// Helper function to handle errors\nconst handleError = error => {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return 'حدث خطأ غير متوقع';\n};\n\n// Company API\nexport const companyAPI = {\n  async getCompany() {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'SELECT * FROM companies LIMIT 1'\n      });\n      return {\n        success: true,\n        data: result[0]\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: handleError(error)\n      };\n    }\n  },\n  async updateCompany(company) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `UPDATE companies SET\n          name = ?, address = ?, phone = ?, email = ?,\n          tax_number = ?, commercial_register = ?, currency = ?\n          WHERE id = ?`,\n        params: [company.name, company.address, company.phone, company.email, company.tax_number, company.commercial_register, company.currency, company.id]\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message || 'حدث خطأ غير متوقع'\n      };\n    }\n  }\n};\n\n// Customers API\nexport const customersAPI = {\n  async getCustomers() {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'SELECT * FROM customers ORDER BY name'\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async getCustomer(id) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'SELECT * FROM customers WHERE id = ?',\n        params: [id]\n      });\n      return {\n        success: true,\n        data: result[0]\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async createCustomer(customer) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `INSERT INTO customers (\n          name, email, phone, address, tax_number,\n          credit_limit, payment_terms, status\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,\n        params: [customer.name, customer.email, customer.phone, customer.address, customer.tax_number, customer.credit_limit, customer.payment_terms, customer.status || 'active']\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async updateCustomer(id, customer) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `UPDATE customers SET\n          name = ?, email = ?, phone = ?, address = ?,\n          tax_number = ?, credit_limit = ?, payment_terms = ?, status = ?\n          WHERE id = ?`,\n        params: [customer.name, customer.email, customer.phone, customer.address, customer.tax_number, customer.credit_limit, customer.payment_terms, customer.status, id]\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async deleteCustomer(id) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'DELETE FROM customers WHERE id = ?',\n        params: [id]\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n};\n\n// Suppliers API\nexport const suppliersAPI = {\n  async getSuppliers() {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'SELECT * FROM suppliers ORDER BY name'\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async createSupplier(supplier) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `INSERT INTO suppliers (\n          name, email, phone, address, tax_number,\n          payment_terms, status\n        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,\n        params: [supplier.name, supplier.email, supplier.phone, supplier.address, supplier.tax_number, supplier.payment_terms, supplier.status || 'active']\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async updateSupplier(id, supplier) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `UPDATE suppliers SET\n          name = ?, email = ?, phone = ?, address = ?,\n          tax_number = ?, payment_terms = ?, status = ?\n          WHERE id = ?`,\n        params: [supplier.name, supplier.email, supplier.phone, supplier.address, supplier.tax_number, supplier.payment_terms, supplier.status, id]\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async deleteSupplier(id) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'DELETE FROM suppliers WHERE id = ?',\n        params: [id]\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n};\n\n// Products API\nexport const productsAPI = {\n  async getProducts() {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'SELECT * FROM products ORDER BY name'\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async createProduct(product) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `INSERT INTO products (\n          name, description, sku, barcode, category,\n          unit_price, cost_price, quantity_in_stock,\n          reorder_level, status\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,\n        params: [product.name, product.description, product.sku, product.barcode, product.category, product.unit_price, product.cost_price, product.quantity_in_stock, product.reorder_level, product.status || 'active']\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async updateProduct(id, product) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `UPDATE products SET\n          name = ?, description = ?, sku = ?, barcode = ?,\n          category = ?, unit_price = ?, cost_price = ?,\n          quantity_in_stock = ?, reorder_level = ?, status = ?\n          WHERE id = ?`,\n        params: [product.name, product.description, product.sku, product.barcode, product.category, product.unit_price, product.cost_price, product.quantity_in_stock, product.reorder_level, product.status, id]\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async deleteProduct(id) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'DELETE FROM products WHERE id = ?',\n        params: [id]\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n};\n\n// Invoices API\nexport const invoicesAPI = {\n  async getInvoices() {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `SELECT i.*, c.name as customer_name\n                FROM invoices i\n                LEFT JOIN customers c ON i.customer_id = c.id\n                ORDER BY i.created_at DESC`\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async createInvoice(invoice) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `INSERT INTO invoices (\n          invoice_number, customer_id, invoice_date, due_date,\n          subtotal, tax_amount, discount_amount, total_amount, status\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,\n        params: [invoice.invoice_number, invoice.customer_id, invoice.invoice_date, invoice.due_date, invoice.subtotal, invoice.tax_amount, invoice.discount_amount, invoice.total_amount, invoice.status || 'draft']\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n};\n\n// Employees API\nexport const employeesAPI = {\n  async getEmployees() {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'SELECT * FROM employees ORDER BY name'\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async createEmployee(employee) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `INSERT INTO employees (\n          name, email, phone, address, position,\n          department, salary, hire_date, status\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,\n        params: [employee.name, employee.email, employee.phone, employee.address, employee.position, employee.department, employee.salary, employee.hire_date, employee.status || 'active']\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n};\n\n// Chart of Accounts API\nexport const accountsAPI = {\n  async getAccounts() {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'SELECT * FROM chart_of_accounts ORDER BY account_code'\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async createAccount(account) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `INSERT INTO chart_of_accounts (\n          account_code, account_name, account_type,\n          parent_account_id, is_active\n        ) VALUES (?, ?, ?, ?, ?)`,\n        params: [account.account_code, account.account_name, account.account_type, account.parent_account_id, account.is_active !== false]\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n};\n\n// Dashboard API\nexport const dashboardAPI = {\n  async getDashboardStats() {\n    try {\n      var _customers$, _suppliers$, _products$, _invoices$, _invoices$2;\n      // Get multiple stats in parallel\n      const [customers, suppliers, products, invoices] = await Promise.all([window.electronAPI.invoke('db-query', {\n        query: 'SELECT COUNT(*) as count FROM customers WHERE status = \"active\"'\n      }), window.electronAPI.invoke('db-query', {\n        query: 'SELECT COUNT(*) as count FROM suppliers WHERE status = \"active\"'\n      }), window.electronAPI.invoke('db-query', {\n        query: 'SELECT COUNT(*) as count FROM products WHERE status = \"active\"'\n      }), window.electronAPI.invoke('db-query', {\n        query: 'SELECT COUNT(*) as count, SUM(total_amount) as total FROM invoices WHERE status != \"cancelled\"'\n      })]);\n      const stats = {\n        customers: ((_customers$ = customers[0]) === null || _customers$ === void 0 ? void 0 : _customers$.count) || 0,\n        suppliers: ((_suppliers$ = suppliers[0]) === null || _suppliers$ === void 0 ? void 0 : _suppliers$.count) || 0,\n        products: ((_products$ = products[0]) === null || _products$ === void 0 ? void 0 : _products$.count) || 0,\n        invoices: ((_invoices$ = invoices[0]) === null || _invoices$ === void 0 ? void 0 : _invoices$.count) || 0,\n        totalSales: ((_invoices$2 = invoices[0]) === null || _invoices$2 === void 0 ? void 0 : _invoices$2.total) || 0\n      };\n      return {\n        success: true,\n        data: stats\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async getRecentInvoices() {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: `SELECT i.*, c.name as customer_name\n                FROM invoices i\n                LEFT JOIN customers c ON i.customer_id = c.id\n                ORDER BY i.created_at DESC\n                LIMIT 5`\n      });\n      return {\n        success: true,\n        data: result\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n};\n\n// Authentication API\nexport const authAPI = {\n  async login(credentials) {\n    try {\n      const result = await window.electronAPI.invoke('db-query', {\n        query: 'SELECT * FROM users WHERE (username = ? OR email = ?) AND password = ? AND status = \"active\"',\n        params: [credentials.username, credentials.username, credentials.password]\n      });\n      if (result.length > 0) {\n        const user = result[0];\n        // Remove password from response\n        delete user.password;\n        return {\n          success: true,\n          data: user\n        };\n      } else {\n        return {\n          success: false,\n          error: 'اسم المستخدم أو كلمة المرور غير صحيحة'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n  async logout() {\n    try {\n      // Clear any session data if needed\n      return {\n        success: true\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n};\n\n// Combined API object for backward compatibility\nexport const dbAPI = {\n  company: companyAPI,\n  customers: customersAPI,\n  suppliers: suppliersAPI,\n  products: productsAPI,\n  invoices: invoicesAPI,\n  employees: employeesAPI,\n  accounts: accountsAPI,\n  dashboard: dashboardAPI,\n  auth: authAPI\n};\n\n// Initialize database with default data\nexport const initializeDatabase = async () => {\n  try {\n    // Check if company exists, if not create default\n    const companyResult = await companyAPI.getCompany();\n    if (!companyResult.success || !companyResult.data) {\n      await window.electronAPI.invoke('db-query', {\n        query: `INSERT INTO companies (\n          name, address, phone, email, tax_number,\n          commercial_register, currency\n        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,\n        params: ['شركة المحاسبة المتقدمة', 'الرياض، المملكة العربية السعودية', '+************', '<EMAIL>', '***************', '**********', 'SAR']\n      });\n    }\n\n    // Check if admin user exists, if not create default\n    const userResult = await window.electronAPI.invoke('db-query', {\n      query: 'SELECT COUNT(*) as count FROM users'\n    });\n    if (userResult[0].count === 0) {\n      await window.electronAPI.invoke('db-query', {\n        query: `INSERT INTO users (\n          name, email, password, role, status\n        ) VALUES (?, ?, ?, ?, ?)`,\n        params: ['المدير العام', '<EMAIL>', 'admin123',\n        // In production, this should be hashed\n        'admin', 'active']\n      });\n    }\n    return {\n      success: true\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};", "map": {"version": 3, "names": ["handleError", "error", "Error", "message", "companyAPI", "getCompany", "result", "window", "electronAPI", "invoke", "query", "success", "data", "updateCompany", "company", "params", "name", "address", "phone", "email", "tax_number", "commercial_register", "currency", "id", "customersAPI", "getCustomers", "getCustomer", "createCustomer", "customer", "credit_limit", "payment_terms", "status", "updateCustomer", "deleteCustomer", "suppliersAPI", "getSuppliers", "createSupplier", "supplier", "updateSupplier", "deleteSupplier", "productsAPI", "getProducts", "createProduct", "product", "description", "sku", "barcode", "category", "unit_price", "cost_price", "quantity_in_stock", "reorder_level", "updateProduct", "deleteProduct", "invoicesAPI", "getInvoices", "createInvoice", "invoice", "invoice_number", "customer_id", "invoice_date", "due_date", "subtotal", "tax_amount", "discount_amount", "total_amount", "employeesAPI", "getEmployees", "createEmployee", "employee", "position", "department", "salary", "hire_date", "accountsAPI", "getAccounts", "createAccount", "account", "account_code", "account_name", "account_type", "parent_account_id", "is_active", "dashboardAPI", "getDashboardStats", "_customers$", "_suppliers$", "_products$", "_invoices$", "_invoices$2", "customers", "suppliers", "products", "invoices", "Promise", "all", "stats", "count", "totalSales", "total", "getRecentInvoices", "authAPI", "login", "credentials", "username", "password", "length", "user", "logout", "dbAPI", "employees", "accounts", "dashboard", "auth", "initializeDatabase", "companyResult", "userResult"], "sources": ["E:/Kimi/src/api/database.ts"], "sourcesContent": ["// Database API layer for Electron app\r\n// This file handles all database operations using IPC communication with the main process\r\n\r\nexport interface DatabaseResponse<T = any> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n}\r\n\r\n// Helper function to handle errors\r\nconst handleError = (error: unknown): string => {\r\n  if (error instanceof Error) {\r\n    return error.message;\r\n  }\r\n  return 'حدث خطأ غير متوقع';\r\n};\r\n\r\n// Company API\r\nexport const companyAPI = {\r\n  async getCompany(): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'SELECT * FROM companies LIMIT 1'\r\n      });\r\n      return { success: true, data: result[0] };\r\n    } catch (error) {\r\n      return { success: false, error: handleError(error) };\r\n    }\r\n  },\r\n\r\n  async updateCompany(company: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `UPDATE companies SET\r\n          name = ?, address = ?, phone = ?, email = ?,\r\n          tax_number = ?, commercial_register = ?, currency = ?\r\n          WHERE id = ?`,\r\n        params: [\r\n          company.name, company.address, company.phone, company.email,\r\n          company.tax_number, company.commercial_register, company.currency,\r\n          company.id\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error: any) {\r\n      return { success: false, error: error.message || 'حدث خطأ غير متوقع' };\r\n    }\r\n  }\r\n};\r\n\r\n// Customers API\r\nexport const customersAPI = {\r\n  async getCustomers(): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'SELECT * FROM customers ORDER BY name'\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async getCustomer(id: number): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'SELECT * FROM customers WHERE id = ?',\r\n        params: [id]\r\n      });\r\n      return { success: true, data: result[0] };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async createCustomer(customer: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `INSERT INTO customers (\r\n          name, email, phone, address, tax_number,\r\n          credit_limit, payment_terms, status\r\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,\r\n        params: [\r\n          customer.name, customer.email, customer.phone, customer.address,\r\n          customer.tax_number, customer.credit_limit, customer.payment_terms,\r\n          customer.status || 'active'\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async updateCustomer(id: number, customer: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `UPDATE customers SET\r\n          name = ?, email = ?, phone = ?, address = ?,\r\n          tax_number = ?, credit_limit = ?, payment_terms = ?, status = ?\r\n          WHERE id = ?`,\r\n        params: [\r\n          customer.name, customer.email, customer.phone, customer.address,\r\n          customer.tax_number, customer.credit_limit, customer.payment_terms,\r\n          customer.status, id\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async deleteCustomer(id: number): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'DELETE FROM customers WHERE id = ?',\r\n        params: [id]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n};\r\n\r\n// Suppliers API\r\nexport const suppliersAPI = {\r\n  async getSuppliers(): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'SELECT * FROM suppliers ORDER BY name'\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async createSupplier(supplier: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `INSERT INTO suppliers (\r\n          name, email, phone, address, tax_number,\r\n          payment_terms, status\r\n        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,\r\n        params: [\r\n          supplier.name, supplier.email, supplier.phone, supplier.address,\r\n          supplier.tax_number, supplier.payment_terms, supplier.status || 'active'\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async updateSupplier(id: number, supplier: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `UPDATE suppliers SET\r\n          name = ?, email = ?, phone = ?, address = ?,\r\n          tax_number = ?, payment_terms = ?, status = ?\r\n          WHERE id = ?`,\r\n        params: [\r\n          supplier.name, supplier.email, supplier.phone, supplier.address,\r\n          supplier.tax_number, supplier.payment_terms, supplier.status, id\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async deleteSupplier(id: number): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'DELETE FROM suppliers WHERE id = ?',\r\n        params: [id]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n};\r\n\r\n// Products API\r\nexport const productsAPI = {\r\n  async getProducts(): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'SELECT * FROM products ORDER BY name'\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async createProduct(product: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `INSERT INTO products (\r\n          name, description, sku, barcode, category,\r\n          unit_price, cost_price, quantity_in_stock,\r\n          reorder_level, status\r\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,\r\n        params: [\r\n          product.name, product.description, product.sku, product.barcode,\r\n          product.category, product.unit_price, product.cost_price,\r\n          product.quantity_in_stock, product.reorder_level, product.status || 'active'\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async updateProduct(id: number, product: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `UPDATE products SET\r\n          name = ?, description = ?, sku = ?, barcode = ?,\r\n          category = ?, unit_price = ?, cost_price = ?,\r\n          quantity_in_stock = ?, reorder_level = ?, status = ?\r\n          WHERE id = ?`,\r\n        params: [\r\n          product.name, product.description, product.sku, product.barcode,\r\n          product.category, product.unit_price, product.cost_price,\r\n          product.quantity_in_stock, product.reorder_level, product.status, id\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async deleteProduct(id: number): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'DELETE FROM products WHERE id = ?',\r\n        params: [id]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n};\r\n\r\n// Invoices API\r\nexport const invoicesAPI = {\r\n  async getInvoices(): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `SELECT i.*, c.name as customer_name\r\n                FROM invoices i\r\n                LEFT JOIN customers c ON i.customer_id = c.id\r\n                ORDER BY i.created_at DESC`\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async createInvoice(invoice: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `INSERT INTO invoices (\r\n          invoice_number, customer_id, invoice_date, due_date,\r\n          subtotal, tax_amount, discount_amount, total_amount, status\r\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,\r\n        params: [\r\n          invoice.invoice_number, invoice.customer_id, invoice.invoice_date,\r\n          invoice.due_date, invoice.subtotal, invoice.tax_amount,\r\n          invoice.discount_amount, invoice.total_amount, invoice.status || 'draft'\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n};\r\n\r\n// Employees API\r\nexport const employeesAPI = {\r\n  async getEmployees(): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'SELECT * FROM employees ORDER BY name'\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async createEmployee(employee: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `INSERT INTO employees (\r\n          name, email, phone, address, position,\r\n          department, salary, hire_date, status\r\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,\r\n        params: [\r\n          employee.name, employee.email, employee.phone, employee.address,\r\n          employee.position, employee.department, employee.salary,\r\n          employee.hire_date, employee.status || 'active'\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n};\r\n\r\n// Chart of Accounts API\r\nexport const accountsAPI = {\r\n  async getAccounts(): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'SELECT * FROM chart_of_accounts ORDER BY account_code'\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async createAccount(account: any): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `INSERT INTO chart_of_accounts (\r\n          account_code, account_name, account_type,\r\n          parent_account_id, is_active\r\n        ) VALUES (?, ?, ?, ?, ?)`,\r\n        params: [\r\n          account.account_code, account.account_name, account.account_type,\r\n          account.parent_account_id, account.is_active !== false\r\n        ]\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n};\r\n\r\n// Dashboard API\r\nexport const dashboardAPI = {\r\n  async getDashboardStats(): Promise<DatabaseResponse> {\r\n    try {\r\n      // Get multiple stats in parallel\r\n      const [customers, suppliers, products, invoices] = await Promise.all([\r\n        window.electronAPI.invoke('db-query', {\r\n          query: 'SELECT COUNT(*) as count FROM customers WHERE status = \"active\"'\r\n        }),\r\n        window.electronAPI.invoke('db-query', {\r\n          query: 'SELECT COUNT(*) as count FROM suppliers WHERE status = \"active\"'\r\n        }),\r\n        window.electronAPI.invoke('db-query', {\r\n          query: 'SELECT COUNT(*) as count FROM products WHERE status = \"active\"'\r\n        }),\r\n        window.electronAPI.invoke('db-query', {\r\n          query: 'SELECT COUNT(*) as count, SUM(total_amount) as total FROM invoices WHERE status != \"cancelled\"'\r\n        })\r\n      ]);\r\n\r\n      const stats = {\r\n        customers: customers[0]?.count || 0,\r\n        suppliers: suppliers[0]?.count || 0,\r\n        products: products[0]?.count || 0,\r\n        invoices: invoices[0]?.count || 0,\r\n        totalSales: invoices[0]?.total || 0\r\n      };\r\n\r\n      return { success: true, data: stats };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async getRecentInvoices(): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: `SELECT i.*, c.name as customer_name\r\n                FROM invoices i\r\n                LEFT JOIN customers c ON i.customer_id = c.id\r\n                ORDER BY i.created_at DESC\r\n                LIMIT 5`\r\n      });\r\n      return { success: true, data: result };\r\n    } catch (error) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n};\r\n\r\n// Authentication API\r\nexport const authAPI = {\r\n  async login(credentials: { username: string; password: string }): Promise<DatabaseResponse> {\r\n    try {\r\n      const result = await window.electronAPI.invoke('db-query', {\r\n        query: 'SELECT * FROM users WHERE (username = ? OR email = ?) AND password = ? AND status = \"active\"',\r\n        params: [credentials.username, credentials.username, credentials.password]\r\n      });\r\n\r\n      if (result.length > 0) {\r\n        const user = result[0];\r\n        // Remove password from response\r\n        delete user.password;\r\n        return { success: true, data: user };\r\n      } else {\r\n        return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' };\r\n      }\r\n    } catch (error: any) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  },\r\n\r\n  async logout(): Promise<DatabaseResponse> {\r\n    try {\r\n      // Clear any session data if needed\r\n      return { success: true };\r\n    } catch (error: any) {\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n};\r\n\r\n// Combined API object for backward compatibility\r\nexport const dbAPI = {\r\n  company: companyAPI,\r\n  customers: customersAPI,\r\n  suppliers: suppliersAPI,\r\n  products: productsAPI,\r\n  invoices: invoicesAPI,\r\n  employees: employeesAPI,\r\n  accounts: accountsAPI,\r\n  dashboard: dashboardAPI,\r\n  auth: authAPI\r\n};\r\n\r\n// Initialize database with default data\r\nexport const initializeDatabase = async (): Promise<DatabaseResponse> => {\r\n  try {\r\n    // Check if company exists, if not create default\r\n    const companyResult = await companyAPI.getCompany();\r\n    if (!companyResult.success || !companyResult.data) {\r\n      await window.electronAPI.invoke('db-query', {\r\n        query: `INSERT INTO companies (\r\n          name, address, phone, email, tax_number,\r\n          commercial_register, currency\r\n        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,\r\n        params: [\r\n          'شركة المحاسبة المتقدمة',\r\n          'الرياض، المملكة العربية السعودية',\r\n          '+************',\r\n          '<EMAIL>',\r\n          '***************',\r\n          '**********',\r\n          'SAR'\r\n        ]\r\n      });\r\n    }\r\n\r\n    // Check if admin user exists, if not create default\r\n    const userResult = await window.electronAPI.invoke('db-query', {\r\n      query: 'SELECT COUNT(*) as count FROM users'\r\n    });\r\n\r\n    if (userResult[0].count === 0) {\r\n      await window.electronAPI.invoke('db-query', {\r\n        query: `INSERT INTO users (\r\n          name, email, password, role, status\r\n        ) VALUES (?, ?, ?, ?, ?)`,\r\n        params: [\r\n          'المدير العام',\r\n          '<EMAIL>',\r\n          'admin123', // In production, this should be hashed\r\n          'admin',\r\n          'active'\r\n        ]\r\n      });\r\n    }\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    return { success: false, error: error.message };\r\n  }\r\n};"], "mappings": "AAAA;AACA;;AAQA;AACA,MAAMA,WAAW,GAAIC,KAAc,IAAa;EAC9C,IAAIA,KAAK,YAAYC,KAAK,EAAE;IAC1B,OAAOD,KAAK,CAACE,OAAO;EACtB;EACA,OAAO,mBAAmB;AAC5B,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxB,MAAMC,UAAUA,CAAA,EAA8B;IAC5C,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN,MAAM,CAAC,CAAC;MAAE,CAAC;IAC3C,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAED,WAAW,CAACC,KAAK;MAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAMY,aAAaA,CAACC,OAAY,EAA6B;IAC3D,IAAI;MACF,MAAMR,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA,uBAAuB;QACfK,MAAM,EAAE,CACND,OAAO,CAACE,IAAI,EAAEF,OAAO,CAACG,OAAO,EAAEH,OAAO,CAACI,KAAK,EAAEJ,OAAO,CAACK,KAAK,EAC3DL,OAAO,CAACM,UAAU,EAAEN,OAAO,CAACO,mBAAmB,EAAEP,OAAO,CAACQ,QAAQ,EACjER,OAAO,CAACS,EAAE;MAEd,CAAC,CAAC;MACF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;MAAoB,CAAC;IACxE;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMqB,YAAY,GAAG;EAC1B,MAAMC,YAAYA,CAAA,EAA8B;IAC9C,IAAI;MACF,MAAMnB,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMuB,WAAWA,CAACH,EAAU,EAA6B;IACvD,IAAI;MACF,MAAMjB,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE,sCAAsC;QAC7CK,MAAM,EAAE,CAACQ,EAAE;MACb,CAAC,CAAC;MACF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN,MAAM,CAAC,CAAC;MAAE,CAAC;IAC3C,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMwB,cAAcA,CAACC,QAAa,EAA6B;IAC7D,IAAI;MACF,MAAMtB,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA,0CAA0C;QAClCK,MAAM,EAAE,CACNa,QAAQ,CAACZ,IAAI,EAAEY,QAAQ,CAACT,KAAK,EAAES,QAAQ,CAACV,KAAK,EAAEU,QAAQ,CAACX,OAAO,EAC/DW,QAAQ,CAACR,UAAU,EAAEQ,QAAQ,CAACC,YAAY,EAAED,QAAQ,CAACE,aAAa,EAClEF,QAAQ,CAACG,MAAM,IAAI,QAAQ;MAE/B,CAAC,CAAC;MACF,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAM6B,cAAcA,CAACT,EAAU,EAAEK,QAAa,EAA6B;IACzE,IAAI;MACF,MAAMtB,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA,uBAAuB;QACfK,MAAM,EAAE,CACNa,QAAQ,CAACZ,IAAI,EAAEY,QAAQ,CAACT,KAAK,EAAES,QAAQ,CAACV,KAAK,EAAEU,QAAQ,CAACX,OAAO,EAC/DW,QAAQ,CAACR,UAAU,EAAEQ,QAAQ,CAACC,YAAY,EAAED,QAAQ,CAACE,aAAa,EAClEF,QAAQ,CAACG,MAAM,EAAER,EAAE;MAEvB,CAAC,CAAC;MACF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAM8B,cAAcA,CAACV,EAAU,EAA6B;IAC1D,IAAI;MACF,MAAMjB,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE,oCAAoC;QAC3CK,MAAM,EAAE,CAACQ,EAAE;MACb,CAAC,CAAC;MACF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAM+B,YAAY,GAAG;EAC1B,MAAMC,YAAYA,CAAA,EAA8B;IAC9C,IAAI;MACF,MAAM7B,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMiC,cAAcA,CAACC,QAAa,EAA6B;IAC7D,IAAI;MACF,MAAM/B,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA,uCAAuC;QAC/BK,MAAM,EAAE,CACNsB,QAAQ,CAACrB,IAAI,EAAEqB,QAAQ,CAAClB,KAAK,EAAEkB,QAAQ,CAACnB,KAAK,EAAEmB,QAAQ,CAACpB,OAAO,EAC/DoB,QAAQ,CAACjB,UAAU,EAAEiB,QAAQ,CAACP,aAAa,EAAEO,QAAQ,CAACN,MAAM,IAAI,QAAQ;MAE5E,CAAC,CAAC;MACF,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMmC,cAAcA,CAACf,EAAU,EAAEc,QAAa,EAA6B;IACzE,IAAI;MACF,MAAM/B,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA,uBAAuB;QACfK,MAAM,EAAE,CACNsB,QAAQ,CAACrB,IAAI,EAAEqB,QAAQ,CAAClB,KAAK,EAAEkB,QAAQ,CAACnB,KAAK,EAAEmB,QAAQ,CAACpB,OAAO,EAC/DoB,QAAQ,CAACjB,UAAU,EAAEiB,QAAQ,CAACP,aAAa,EAAEO,QAAQ,CAACN,MAAM,EAAER,EAAE;MAEpE,CAAC,CAAC;MACF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMoC,cAAcA,CAAChB,EAAU,EAA6B;IAC1D,IAAI;MACF,MAAMjB,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE,oCAAoC;QAC3CK,MAAM,EAAE,CAACQ,EAAE;MACb,CAAC,CAAC;MACF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMqC,WAAW,GAAG;EACzB,MAAMC,WAAWA,CAAA,EAA8B;IAC7C,IAAI;MACF,MAAMnC,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMuC,aAAaA,CAACC,OAAY,EAA6B;IAC3D,IAAI;MACF,MAAMrC,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA;AACA,gDAAgD;QACxCK,MAAM,EAAE,CACN4B,OAAO,CAAC3B,IAAI,EAAE2B,OAAO,CAACC,WAAW,EAAED,OAAO,CAACE,GAAG,EAAEF,OAAO,CAACG,OAAO,EAC/DH,OAAO,CAACI,QAAQ,EAAEJ,OAAO,CAACK,UAAU,EAAEL,OAAO,CAACM,UAAU,EACxDN,OAAO,CAACO,iBAAiB,EAAEP,OAAO,CAACQ,aAAa,EAAER,OAAO,CAACZ,MAAM,IAAI,QAAQ;MAEhF,CAAC,CAAC;MACF,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMiD,aAAaA,CAAC7B,EAAU,EAAEoB,OAAY,EAA6B;IACvE,IAAI;MACF,MAAMrC,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA;AACA,uBAAuB;QACfK,MAAM,EAAE,CACN4B,OAAO,CAAC3B,IAAI,EAAE2B,OAAO,CAACC,WAAW,EAAED,OAAO,CAACE,GAAG,EAAEF,OAAO,CAACG,OAAO,EAC/DH,OAAO,CAACI,QAAQ,EAAEJ,OAAO,CAACK,UAAU,EAAEL,OAAO,CAACM,UAAU,EACxDN,OAAO,CAACO,iBAAiB,EAAEP,OAAO,CAACQ,aAAa,EAAER,OAAO,CAACZ,MAAM,EAAER,EAAE;MAExE,CAAC,CAAC;MACF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMkD,aAAaA,CAAC9B,EAAU,EAA6B;IACzD,IAAI;MACF,MAAMjB,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE,mCAAmC;QAC1CK,MAAM,EAAE,CAACQ,EAAE;MACb,CAAC,CAAC;MACF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMmD,WAAW,GAAG;EACzB,MAAMC,WAAWA,CAAA,EAA8B;IAC7C,IAAI;MACF,MAAMjD,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA;MACM,CAAC,CAAC;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMqD,aAAaA,CAACC,OAAY,EAA6B;IAC3D,IAAI;MACF,MAAMnD,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA,6CAA6C;QACrCK,MAAM,EAAE,CACN0C,OAAO,CAACC,cAAc,EAAED,OAAO,CAACE,WAAW,EAAEF,OAAO,CAACG,YAAY,EACjEH,OAAO,CAACI,QAAQ,EAAEJ,OAAO,CAACK,QAAQ,EAAEL,OAAO,CAACM,UAAU,EACtDN,OAAO,CAACO,eAAe,EAAEP,OAAO,CAACQ,YAAY,EAAER,OAAO,CAAC1B,MAAM,IAAI,OAAO;MAE5E,CAAC,CAAC;MACF,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAM+D,YAAY,GAAG;EAC1B,MAAMC,YAAYA,CAAA,EAA8B;IAC9C,IAAI;MACF,MAAM7D,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMiE,cAAcA,CAACC,QAAa,EAA6B;IAC7D,IAAI;MACF,MAAM/D,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA,6CAA6C;QACrCK,MAAM,EAAE,CACNsD,QAAQ,CAACrD,IAAI,EAAEqD,QAAQ,CAAClD,KAAK,EAAEkD,QAAQ,CAACnD,KAAK,EAAEmD,QAAQ,CAACpD,OAAO,EAC/DoD,QAAQ,CAACC,QAAQ,EAAED,QAAQ,CAACE,UAAU,EAAEF,QAAQ,CAACG,MAAM,EACvDH,QAAQ,CAACI,SAAS,EAAEJ,QAAQ,CAACtC,MAAM,IAAI,QAAQ;MAEnD,CAAC,CAAC;MACF,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMuE,WAAW,GAAG;EACzB,MAAMC,WAAWA,CAAA,EAA8B;IAC7C,IAAI;MACF,MAAMrE,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMyE,aAAaA,CAACC,OAAY,EAA6B;IAC3D,IAAI;MACF,MAAMvE,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA,iCAAiC;QACzBK,MAAM,EAAE,CACN8D,OAAO,CAACC,YAAY,EAAED,OAAO,CAACE,YAAY,EAAEF,OAAO,CAACG,YAAY,EAChEH,OAAO,CAACI,iBAAiB,EAAEJ,OAAO,CAACK,SAAS,KAAK,KAAK;MAE1D,CAAC,CAAC;MACF,OAAO;QAAEvE,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMgF,YAAY,GAAG;EAC1B,MAAMC,iBAAiBA,CAAA,EAA8B;IACnD,IAAI;MAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,WAAA;MACF;MACA,MAAM,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnExF,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACpCC,KAAK,EAAE;MACT,CAAC,CAAC,EACFH,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACpCC,KAAK,EAAE;MACT,CAAC,CAAC,EACFH,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACpCC,KAAK,EAAE;MACT,CAAC,CAAC,EACFH,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACpCC,KAAK,EAAE;MACT,CAAC,CAAC,CACH,CAAC;MAEF,MAAMsF,KAAK,GAAG;QACZN,SAAS,EAAE,EAAAL,WAAA,GAAAK,SAAS,CAAC,CAAC,CAAC,cAAAL,WAAA,uBAAZA,WAAA,CAAcY,KAAK,KAAI,CAAC;QACnCN,SAAS,EAAE,EAAAL,WAAA,GAAAK,SAAS,CAAC,CAAC,CAAC,cAAAL,WAAA,uBAAZA,WAAA,CAAcW,KAAK,KAAI,CAAC;QACnCL,QAAQ,EAAE,EAAAL,UAAA,GAAAK,QAAQ,CAAC,CAAC,CAAC,cAAAL,UAAA,uBAAXA,UAAA,CAAaU,KAAK,KAAI,CAAC;QACjCJ,QAAQ,EAAE,EAAAL,UAAA,GAAAK,QAAQ,CAAC,CAAC,CAAC,cAAAL,UAAA,uBAAXA,UAAA,CAAaS,KAAK,KAAI,CAAC;QACjCC,UAAU,EAAE,EAAAT,WAAA,GAAAI,QAAQ,CAAC,CAAC,CAAC,cAAAJ,WAAA,uBAAXA,WAAA,CAAaU,KAAK,KAAI;MACpC,CAAC;MAED,OAAO;QAAExF,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEoF;MAAM,CAAC;IACvC,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMiG,iBAAiBA,CAAA,EAA8B;IACnD,IAAI;MACF,MAAM9F,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE;AACf;AACA;AACA;AACA;MACM,CAAC,CAAC;MACF,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,IAAI,EAAEN;MAAO,CAAC;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACd,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMkG,OAAO,GAAG;EACrB,MAAMC,KAAKA,CAACC,WAAmD,EAA6B;IAC1F,IAAI;MACF,MAAMjG,MAAM,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QACzDC,KAAK,EAAE,8FAA8F;QACrGK,MAAM,EAAE,CAACwF,WAAW,CAACC,QAAQ,EAAED,WAAW,CAACC,QAAQ,EAAED,WAAW,CAACE,QAAQ;MAC3E,CAAC,CAAC;MAEF,IAAInG,MAAM,CAACoG,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMC,IAAI,GAAGrG,MAAM,CAAC,CAAC,CAAC;QACtB;QACA,OAAOqG,IAAI,CAACF,QAAQ;QACpB,OAAO;UAAE9F,OAAO,EAAE,IAAI;UAAEC,IAAI,EAAE+F;QAAK,CAAC;MACtC,CAAC,MAAM;QACL,OAAO;UAAEhG,OAAO,EAAE,KAAK;UAAEV,KAAK,EAAE;QAAwC,CAAC;MAC3E;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnB,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMyG,MAAMA,CAAA,EAA8B;IACxC,IAAI;MACF;MACA,OAAO;QAAEjG,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAU,EAAE;MACnB,OAAO;QAAEU,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAEA,KAAK,CAACE;MAAQ,CAAC;IACjD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAM0G,KAAK,GAAG;EACnB/F,OAAO,EAAEV,UAAU;EACnBsF,SAAS,EAAElE,YAAY;EACvBmE,SAAS,EAAEzD,YAAY;EACvB0D,QAAQ,EAAEpD,WAAW;EACrBqD,QAAQ,EAAEvC,WAAW;EACrBwD,SAAS,EAAE5C,YAAY;EACvB6C,QAAQ,EAAErC,WAAW;EACrBsC,SAAS,EAAE7B,YAAY;EACvB8B,IAAI,EAAEZ;AACR,CAAC;;AAED;AACA,OAAO,MAAMa,kBAAkB,GAAG,MAAAA,CAAA,KAAuC;EACvE,IAAI;IACF;IACA,MAAMC,aAAa,GAAG,MAAM/G,UAAU,CAACC,UAAU,CAAC,CAAC;IACnD,IAAI,CAAC8G,aAAa,CAACxG,OAAO,IAAI,CAACwG,aAAa,CAACvG,IAAI,EAAE;MACjD,MAAML,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QAC1CC,KAAK,EAAE;AACf;AACA;AACA,uCAAuC;QAC/BK,MAAM,EAAE,CACN,wBAAwB,EACxB,kCAAkC,EAClC,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,KAAK;MAET,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMqG,UAAU,GAAG,MAAM7G,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;MAC7DC,KAAK,EAAE;IACT,CAAC,CAAC;IAEF,IAAI0G,UAAU,CAAC,CAAC,CAAC,CAACnB,KAAK,KAAK,CAAC,EAAE;MAC7B,MAAM1F,MAAM,CAACC,WAAW,CAACC,MAAM,CAAC,UAAU,EAAE;QAC1CC,KAAK,EAAE;AACf;AACA,iCAAiC;QACzBK,MAAM,EAAE,CACN,cAAc,EACd,mBAAmB,EACnB,UAAU;QAAE;QACZ,OAAO,EACP,QAAQ;MAEZ,CAAC,CAAC;IACJ;IAEA,OAAO;MAAEJ,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;IACd,OAAO;MAAEU,OAAO,EAAE,KAAK;MAAEV,KAAK,EAAEA,KAAK,CAACE;IAAQ,CAAC;EACjD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}